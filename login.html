<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title class="tdk1"></title>
    <meta class="tdk2" name="description" content="" />
    <meta class="tdk3" name="keywords" content="" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <meta http-equiv="Cache-Control" content="no-transform" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />
    <meta name="referrer" content="origin" />
    <link rel="shortcut icon" href="https://libs.eoffcn.com/ico/offcn.ico" />
    <link rel="stylesheet" href="./css/style.css" />
    <script src="js/vue.js"></script>
    <script type="text/javascript" src="js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="https://libs.eoffcn.com/layui/layer/3.5.1/layer.js"></script>
    <script>
      $(function() {
          window.onresize = auto;
          auto()
          function auto() {
              var deviceWidth = document.documentElement.clientWidth;
              if (deviceWidth > 750) deviceWidth = 750;
              document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
          }
      })
      </script>
  </head>

  <body>
    <div id="app">
      <div class="wrapper">
        <div class="login_title">确认领取</div>
        <div class="login_box">
          <div class="input_box">
            <div class="input_item">
              <input type="text" maxlength="11"  v-model="mobile" @input="hanldPhone" placeholder="请输入手机号">
            </div>
            <div class="input_item" v-if="hasPhone">
              <input type="text" maxlength="4" v-model="captcha" placeholder="请输入验证码">
              <span @click="getCode" class="getYZM" :disabled="isLoading">{{ countdown > 0? countdown+'s后重新获取' : '发送验证码' }}</span>
            </div>
          </div>
          <div @click="submitFn" class="submit">立即领取</div>
          <div class="login_info">*手机号为领取课程唯一凭证，请核对无误后 进行领取操作</div>
        </div>
      </div>
    </div>
  </body>
  <script>
    const { createApp } = Vue
    const phoneRegex = /^1[3-9]\d{9}$/;
    createApp({
        data() {
            return {
              mobile: '',
              captcha: '',
              isLoading: false,
              countdown: 0,
              kkcode: '',
              hasPhone:false
            }
        },
        mounted(){
         this.kkcode=this.getQueryString('kkcode')
        },
        methods: {
          getQueryString(name) {
              var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
              var r = window.location.search.substr(1).match(reg);
              if (r != null) {
                  return unescape(r[2]);
              }
              return null;
          },
          hanldPhone(){
            if(this.mobile.length ==11){
              if (!phoneRegex.test(this.mobile)) {
                layer.msg('请输入正确的手机号')
                return;
              }
              var telist=  JSON.parse(localStorage.getItem('telList')) || []
              var data  = telist.filter(item=>{
                return  item.phone == this.mobile
              })
              if(data.length>0){
                this.captcha=data[0].captcha
                this.hasPhone = false
              }else{
                this.captcha= ''
                this.hasPhone = true
              }
            }
          },
          setCun(phone,captcha){
            var telist=  JSON.parse(localStorage.getItem('telList')) || []
            console.log(telist)
            telist.push({
              phone,captcha
            })
            localStorage.setItem('telList',JSON.stringify(telist))
          },
          getCode(){
            if (this.isLoading) return; // 如果正在加载，直接返回
            if (!this.mobile) {
                layer.msg('请输入手机号')
                return;
            }
            if (!phoneRegex.test(this.mobile)) {
              layer.msg('请输入正确的手机号')
              return;
            }
            this.isLoading = true;
            this.countdown = 60; // 设置倒计时为60秒
            $.ajax({
              url: 'https://applets.zgsydw.com/api/opencourse/getCode',
              dataType: 'json',
              type: 'GET',
              jsonp:"callback",
              data: {
                mobile:this.mobile
              },
              success: (json)=> {
                if(json.status==1){
                  layer.msg('发送成功')
                }else{
                  layer.msg(json.msg)
                }
              },
              complete: () => {
                // 启动倒计时
                const timer = setInterval(() => {
                  this.countdown--;
                  if (this.countdown <= 0) {
                    clearInterval(timer);
                    this.isLoading = false;
                  }
                }, 1000);
              }
            })
          },
          submitFn(){
            if (!this.mobile) {
                layer.msg('请输入手机号')
                return;
            }
            if (!phoneRegex.test(this.mobile)) {
              layer.msg('请输入正确的手机号')
              return;
            }
            if (!this.captcha) {
                layer.msg('请输入验证码')
                return;
            }
            $.ajax({
              url: 'https://applets.zgsydw.com/api/opencourse/openCourseTuShuApp',
              dataType: 'json',
              type: 'GET',
              jsonp:"callback",
              data: {
                province: 'jz',
                mobile: this.mobile,
                captcha: this.captcha,
                kkcode: this.kkcode
              },
              success: (json)=> {
                if(json.code==-1){
                  layer.msg(json.message)
                  if(json.message=='该手机号已经领取过此课程！'){
                    if(this.hasPhone){
                      this.setCun( this.mobile, 1111)
                    }
                  }
                }else{
                  if(this.hasPhone){
                    this.setCun( this.mobile, this.captcha)
                  }
                  if(this.getQueryString('appQrScan')==1){
                    layer.msg('课程领取成功，重新进入app即可听课')
                  }else{
                    layer.msg('领取成功')
                    setTimeout(()=>{
                      window.location.href="https://www.offcn.com/zg/syb/tushuapp/"
                    },1000)
                  }
                }
              },
             
            })
          }
        }
    }).mount('#app')
  </script>
</html>
