<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,minimal-ui,user-scalable=no" />
    <meta name="renderer" content="webkit" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="format-detaction" content="telphone=no,email=no" />
    <!-- 针对手持设备优化，主要是针对一些老的不识别viewport的浏览器，比如黑莓 -->
    <meta name="HandheldFriendly" content="true" />
    <!-- 微软的老式浏览器 -->
    <meta name="MobileOptimized" content="320" />
    <!-- UC应用模式 -->
    <meta name="browsemode" content="application" />
    <!-- QQ应用模式 -->
    <meta name="x5-page-mode" content="app" />
    <!-- windows phone 点击无高光 -->
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="keywords" content="中公教育" />
    <meta name="description" content="中公教育" />
    <title>中公教育</title>
    <link href="css/style.css?v=12" type="text/css" rel="stylesheet" />
    <script src="js/vue.js"></script>
    <script type="text/javascript" src="js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="js/data.js?v2.3"></script>
    <script>
    $(function() {
        window.onresize = auto;
        auto()
        function auto() {
            var deviceWidth = document.documentElement.clientWidth;
            if (deviceWidth > 750) deviceWidth = 750;
            document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
        }
    })
    </script>
</head>

<body>
    <div id="app">
        <div class="cover">
            <div class="cover-title">{{province}}公务员考试</div>
            <img src="./images/banner.png" alt="Banner">
        </div>
        <div class="title">图书辅学精选课程·{{province}}公务员考试·行测申论 课程包</div>
        <div class="info">
          <img src="images/info.png" alt="">
        </div>
        <div class="bottom">
          <span @click="receiveFn">立即领取>></span>
        </div>
    </div>
</body>
  <script>
    
    const { createApp } = Vue
    createApp({
        data() {
            return {
              province: '',
              kkcode:''
            }
        },
        mounted(){
          const paramValue = this.getQueryString('type') || 'hebei'
          provinceData.forEach((item) => {
            if (item.province_code === paramValue) {
              this.province = item.province_name;
              this.kkcode =item.kkcode
            }
          });
        },
        methods: {
          getQueryString(name) {
              var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
              var r = window.location.search.substr(1).match(reg);
              if (r != null) {
                  return unescape(r[2]);
              }
              return null;
          },
          receiveFn(){
            // window.location.href=`./login.html?kkcode=${this.kkcode}`
            window.location.href=`./login.html?kkcode=${this.kkcode}&appQrScan=${this.getQueryString('appQrScan') || 0}`
          },
        }
    }).mount('#app')
  </script>
</html>