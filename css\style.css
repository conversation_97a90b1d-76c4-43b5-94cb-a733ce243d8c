@charset "UTF-8";

* {
    padding: 0;
    margin: 0;
}

img {
    border: none;
    display: block;
}

ul,
ul li,
dl,
dl dt,
dl dd {
    display: block;
    list-style-type: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: 100%;
}

a {
    color: #000;
    text-decoration: none;
}

a:hover {
    color: #e00;
    text-decoration: none;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ".";
    clear: both;
    height: 0;
}

body {
    font-family: 'Microsoft Yahei';
    font-size: 10px;
}
.box, body{
    max-width: 7.5rem;
    margin: 0 auto;
}
.cover{
    position: relative;
}
.cover img{
    width: 100%;
}
.cover-title {
    font-size: .4rem;
    color: rgb(100, 60, 46);
    position: absolute;
    top: 1.7rem;
    left: 1.7rem;
    text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
}
.title {
    font-size: .35rem;
    padding: .37rem .55rem;
    font-weight: bold;
    line-height: 1.5;
}
.info img{
    width: 100%;
}
.bottom{
    position: fixed;
    bottom: 1.06667rem;
    display: flex;
    left: 0;
    right: 0;
    justify-content: center;
    align-items: center;
}
.bottom span{
    width: 3.52rem;
    height: 0.65rem;
    border-radius: .1rem;
    font-size: .4rem;
    line-height: .65rem;
    text-align: center;
    background: #fecc33;
    cursor: pointer;
}


.wrapper {
    width: 7.5rem;
    background: url(../images/bg.png) no-repeat top;
    background-size: 7.5rem
}

.login_title {
    font-size: .44rem;
    font-weight: 700;
    color: #fff;
    padding-top:1rem;
    padding-left: .66667rem
}
.input_box{
    margin: 0 .4rem;
}
.input_item {
    width: 100%;
    height: .8rem;
    border: .02667rem solid #d9d9d9;
    border-radius: .58667rem;
    overflow: hidden;
    margin-bottom: .53333rem
}
.input_item input{
    width: 100%;
    height: .8rem;
    border: 0;
    outline: none;
    text-indent: 2em;
}
.input_item:last-child{
    position: relative;
}
.input_item:last-child input{
    width: 70%;
}
.input_item:last-child span{
    font-size: .24rem;
    position: absolute;
    line-height: .8rem;
    right: .8rem;
}
.input_box {
    display: flex;
    flex-direction: column;
    align-items: center
}

.login_box {
    margin-top: 2.8rem
}

.submit {
    margin:.4rem .3rem .3rem .3rem;
    height: .8rem;
    border-radius: .58667rem;
    background: #ff3838;
    color: #fff;
    font-size: .3rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.getYZM {
    color: #ff3838
}

.login_info {
    font-size: .3rem;
    line-height: 1.5;
    margin:0 .3rem;
}
